<?php
/**
 * Form editor template - Modular version
 */

// Get form data if editing
$form_id = isset($_GET['id']) ? $_GET['id'] : 0;
$is_new = ($form_id === 0 || $form_id === 'new');
$form_id = $is_new ? 0 : intval($form_id);
$form_data = array();
$form_fields = array();
$form_settings = array();

if (!$is_new) {
    global $wpdb;
    $table_name = $wpdb->prefix . 'form_elrakami_forms';
    $form = $wpdb->get_row($wpdb->prepare("SELECT * FROM {$table_name} WHERE id = %d", $form_id));

    if ($form) {
        $form_data = array(
            'id' => $form->id,
            'title' => $form->title,
            'description' => $form->description,
            'status' => $form->status,
        );

        $form_settings = Form_Elrakami_Helper::unserialize_data($form->settings);
        $form_fields = Form_Elrakami_Helper::unserialize_data($form->fields);

        // دمج الإعدادات الافتراضية مع الإعدادات الموجودة لضمان وجود جميع الإعدادات الجديدة
        $default_settings = array(
            // إعدادات ملخص الطلب
            'summary_display_mode' => 'visible',
            'summary_default_state' => 'open',
            'show_price_in_button' => 'yes',
            'summary_bg_color' => 'rgba(37, 99, 235, 0.05)',
            'summary_border_color' => 'rgba(37, 99, 235, 0.1)',
            'summary_header_bg_color' => '#f1f5f9',
            'summary_title_color' => '#1e293b',
            'summary_price_color' => '#3730a3',
            'summary_price_bg_color' => 'rgba(55, 48, 163, 0.05)',
            'summary_border_radius' => '12',
        );

        // دمج الإعدادات الافتراضية مع الإعدادات الموجودة
        $form_settings = array_merge($default_settings, $form_settings ?: array());
    }
}

// Default values for new form
if (empty($form_data)) {
    $form_data = array(
        'id' => 0,
        'title' => '',
        'description' => '',
        'status' => 'active',
    );
}

if (empty($form_settings)) {
    $form_settings = array(
        // زر الطلب
        'button_text' => 'إتمام الطلب',
        'button_color' => '#3730a3',
        'button_border_radius' => '4',
        'button_size' => 'medium',
        'button_text_color' => '#ffffff',
        'button_hover_effect' => 'shadow',
        'button_icon' => 'check',
        'button_icon_position' => 'right',
        'button_gradient' => 'yes',
        'button_gradient_color' => '#312e81',
        'show_sticky_bar' => 'yes',

        // إعدادات الشريط المثبت
        'sticky_bar_button_color' => '#3730a3',
        'sticky_bar_button_gradient' => 'yes',
        'sticky_bar_button_gradient_color' => '#312e81',

        // الألوان والتصميم
        'icons_color' => '#3730a3',
        'card_bg_color' => '#ffffff',
        'text_color' => '#1e293b',
        'card_border_radius' => '8',
        'card_shadow' => 'medium',

        // حدود النموذج
        'form_border_style' => 'dotted',
        'form_border_color' => '#3730a3',
        'form_border_width' => '2',

        // الهوامش والمسافات
        'form_padding' => 'medium',
        'form_margin' => 'medium',

        // الحقول
        'fields_border_radius' => '4',
        'fields_spacing' => 'medium',
        'fields_bg_color' => '#f8fafc',
        'fields_border_color' => '#e2e8f0',
        'fields_layout' => 'vertical',
        'fields_column_gap' => 'medium',

        // الأيقونات والنصوص
        'icons_position' => 'right',
        'labels_font_weight' => 'bold',

        // عناصر النموذج
        'summary_position' => 'bottom',

        // إعدادات ملخص الطلب
        'summary_display_mode' => 'visible',
        'summary_default_state' => 'open',
        'show_price_in_button' => 'yes',
        'summary_bg_color' => 'rgba(37, 99, 235, 0.05)',
        'summary_border_color' => 'rgba(37, 99, 235, 0.1)',
        'summary_header_bg_color' => '#f1f5f9',
        'summary_title_color' => '#1e293b',
        'summary_price_color' => '#3730a3',
        'summary_price_bg_color' => 'rgba(55, 48, 163, 0.05)',
        'summary_border_radius' => '12',
    );
}

// Default fields for new form
if (empty($form_fields)) {
    $form_fields = array(
        array(
            'id' => 'full_name',
            'label' => 'الاسم الكامل',
            'type' => 'text',
            'required' => true,
            'visible' => true,
            'options' => array(),
            'placeholder' => '',
            'default_value' => '',
            'description' => '',
        ),
        array(
            'id' => 'phone',
            'label' => 'رقم الهاتف',
            'type' => 'tel',
            'required' => true,
            'visible' => true,
            'options' => array(),
            'placeholder' => '',
            'default_value' => '',
            'description' => '',
        ),
        array(
            'id' => 'state',
            'label' => 'الولاية',
            'type' => 'select',
            'required' => true,
            'visible' => true,
            'options' => array(),
            'placeholder' => 'اختر الولاية',
            'default_value' => '',
            'description' => '',
        ),
        array(
            'id' => 'municipality',
            'label' => 'البلدية',
            'type' => 'select',
            'required' => true,
            'visible' => true,
            'options' => array(),
            'placeholder' => 'اختر البلدية',
            'default_value' => '',
            'description' => 'يتم تحديثها تلقائياً بناءً على اختيار الولاية',
        ),
        array(
            'id' => 'address',
            'label' => 'العنوان التفصيلي',
            'type' => 'text',
            'required' => true,
            'visible' => true,
            'options' => array(),
            'placeholder' => 'أدخل العنوان التفصيلي',
            'default_value' => '',
            'description' => 'مثال: شارع الشهداء، حي الزيتون، عمارة رقم 35',
        ),
    );
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline">
        <?php echo $is_new ? 'إضافة نموذج جديد' : 'إعدادات النموذج: ' . esc_html($form_data['title']); ?>
    </h1>
    <a href="<?php echo admin_url('admin.php?page=form-elrakami'); ?>" class="page-title-action">العودة إلى القائمة</a>
    <hr class="wp-header-end">

    <!-- شريط الشعار -->
    <?php include(plugin_dir_path(dirname(__FILE__)) . 'partials/header-bar.php'); ?>

    <form method="post" action="" id="form-editor-form">
        <?php wp_nonce_field('form_elrakami_save_form', 'form_elrakami_form_nonce'); ?>
        <input type="hidden" name="form_id" value="<?php echo $form_data['id']; ?>">

        <div id="poststuff">
            <div id="post-body" class="metabox-holder columns-1">
                <div id="post-body-content">

                    <div class="form-settings-wrapper">
                        <h2><span>إعدادات النموذج</span></h2>
                        <div class="design-settings-container">
                            <?php include plugin_dir_path( __FILE__ ) . 'form-settings-sidebar.php'; ?>
                        </div>
                    </div>
                </div>
            </div>
            <br class="clear">
        </div>
    </form>

    <!-- تم إزالة النافذة المنبثقة القديمة واستبدالها بنظام التعديل المدمج -->

    <style>
    /* تنسيق حاوية إعدادات النموذج */
    .form-settings-wrapper {
        background: #fff;
        border: 1px solid #ccd0d4;
        box-shadow: 0 1px 1px rgba(0,0,0,.04);
        margin-bottom: 20px;
    }

    .form-settings-wrapper h2 {
        border-bottom: 1px solid #ccd0d4;
        font-size: 14px;
        padding: 8px 12px;
        margin: 0;
        line-height: 1.4;
        font-weight: 600;
    }
    </style>
</div>