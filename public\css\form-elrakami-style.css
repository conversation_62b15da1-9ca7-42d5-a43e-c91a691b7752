/**
 * Form Elrakami - Main Styling to Match Custom Order Form
 * هذا الملف يحتوي على أنماط CSS للنماذج المعروضة في الواجهة الأمامية للموقع
 * التصميم مستوحى من إضافة custom-order-form
 * تم تعديله لإزالة اعتماد Select2
 */

/* استيراد خط Rubik Arabic من Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Rubik:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/**
 * إزالة عالمية وقوية جداً لسهم القوائم المنسدلة من جميع المتصفحات
 * هذه الأنماط تطبق على جميع عناصر select في الموقع
 */

/* إزالة السهم بقوة قصوى من جميع عناصر select */
select,
select:focus,
select:hover,
select:active,
select:visited {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background-position: unset !important;
    background-repeat: no-repeat !important;
    background-size: 0 !important;
}

/* إزالة السهم من متصفحات Microsoft */
select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

select::-ms-value {
    background: transparent !important;
    color: inherit !important;
}

/* إزالة السهم من Firefox بطرق متعددة */
@-moz-document url-prefix() {
    select {
        -moz-appearance: none !important;
        background-image: none !important;
        text-indent: 0.01px !important;
        text-overflow: '' !important;
    }
}

/* إزالة السهم من WebKit browsers (Chrome, Safari, Edge الجديد) */
select::-webkit-appearance {
    -webkit-appearance: none !important;
}

select::-webkit-inner-spin-button,
select::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
}

/* إزالة السهم من جميع عناصر select بغض النظر عن الكلاس أو المعرف */
* select,
*:before select,
*:after select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/**
 * متغيرات CSS الرئيسية
 * يمكن تخصيصها من إعدادات الإضافة
 */
:root {
    --form-primary-color: #2563eb;
    --form-primary-rgb: 37, 99, 235; /* قيم RGB للون الرئيسي للاستخدام في الشفافية */
    --form-primary-hover: #1d4ed8;
    --form-error-color: #ef4444;
    --form-error-rgb: 239, 68, 68; /* قيم RGB للون الخطأ */
    --form-border-color: #e2e8f0;
    --form-bg-color: #ffffff;
    --form-text-color: #1e293b;
    --form-placeholder-color: #94a3b8;
    --form-input-bg: #f8fafc;
    --form-input-border: #e2e8f0;
    --form-input-focus-border: #93c5fd;
    --form-label-color: #475569;
    --form-padding: 20px;
    --form-radius: 8px;
    --form-input-radius: 6px;
    --form-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    /* متغيرات الشريط المثبت */
    --sticky-button-color: #3730a3;
    --sticky-button-text-color: #ffffff;
    --sticky-button-gradient-color: #312e81;
    --sticky-button-gradient-direction: to bottom;
}

/**
 * هذه المتغيرات سيتم تجاوزها عن طريق الإعدادات المخصصة في وسم style مباشرة في النموذج
 * لا تقم بتعديلها هنا، بل استخدم خيارات النموذج في لوحة التحكم
 */
.form-elrakami-container {
    --form-primary-color: #2563eb;
    --form-primary-rgb: 37, 99, 235; /* قيم RGB للون الرئيسي */
    --form-primary-hover: #1d4ed8;
    --form-bg-color: #ffffff;
    --form-padding: 20px;
}

/**
 * حاوية النموذج الرئيسية
 */
.form-elrakami-container {
    width: 100%;
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    color: var(--form-text-color);
    margin-bottom: 30px;
    direction: rtl;
}

/* تقليل الهوامش الخارجية للنموذج على الهاتف */
@media (max-width: 576px) {
    .form-elrakami-container {
        margin: 0 !important;
        padding: 0 !important;
    }
}

/**
 * نموذج الطلب
 */
.form-elrakami-form {
    background-color: var(--form-bg-color);
    border-radius: var(--form-radius);
    padding: var(--form-padding);
    box-shadow: var(--form-shadow);
    max-width: 100%;
    width: 100%;
    margin: 0 auto;
}

/* تقليل الهوامش الداخلية للنموذج على الهاتف */
@media (max-width: 576px) {
    .form-elrakami-form {
        padding: 10px !important;
        border-radius: 6px !important;
    }
}

/**
 * عنوان النموذج
 */
.form-elrakami-form h2 {
    color: var(--form-text-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 0.8rem;
    padding-bottom: 0.6rem;
    border-bottom: 1px solid var(--form-border-color);
    text-align: center;
}

.form-elrakami-form h2 i {
    margin-left: 8px;
    color: var(--form-primary-color);
}

/**
 * وصف النموذج
 */
.form-elrakami-form .form-elrakami-description {
    font-size: 0.9rem;
    margin-bottom: 1.2rem;
    text-align: center;
    color: var(--form-label-color);
}

/**
 * مجموعة الحقول
 */
.form-elrakami-form .form-group {
    margin-bottom: 1.25rem;
    position: relative;
}

/**
 * العناوين
 */
.form-elrakami-form label {
    display: block;
    margin-bottom: 0.4rem;
    font-weight: 500;
    font-size: 0.85rem;
    color: var(--form-label-color);
}

.form-elrakami-form label .required {
    color: var(--form-error-color);
    margin-right: 3px;
}

/**
 * مجموعة الإدخال مع الأيقونات
 */
.form-elrakami-form .input-group {
    display: flex;
    align-items: center;
    width: 100%;
    position: relative;
    margin-bottom: 0.5rem;
    border-radius: var(--form-input-radius, 4px);
    overflow: hidden;
}

.form-elrakami-form .input-group-text {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem;
    font-size: 1rem;
    background-color: var(--form-primary-color) !important;
    color: white !important;
    border: 1px solid var(--form-primary-color) !important;
    transition: all 0.3s ease-in-out !important;
}

/* تطبيق لون الأيقونات المخصص عن طريق أسلوب إضافي ذو أولوية أعلى */
.form-elrakami-container .form-elrakami-form .input-group-text {
    background-color: var(--form-primary-color) !important;
    border-color: var(--form-primary-color) !important;
}

/* موضع الأيقونات على اليمين (الافتراضي للعربية) */
.form-elrakami-container.icons-right .input-group-text {
    border-radius: 0 var(--form-input-radius) var(--form-input-radius) 0;
    order: -1;
}

/* موضع الأيقونات على اليسار */
.form-elrakami-container.icons-left .input-group-text {
    border-radius: var(--form-input-radius) 0 0 var(--form-input-radius);
    order: 1;
}

/**
 * حقول الإدخال
 */
.form-elrakami-form input[type="text"],
.form-elrakami-form input[type="email"],
.form-elrakami-form input[type="tel"],
.form-elrakami-form input[type="number"],
.form-elrakami-form textarea,
.form-elrakami-form select {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--form-text-color);
    background-color: var(--form-input-bg);
    border: 1px solid var(--form-input-border);
    border-radius: var(--form-input-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* حقول الإدخال في مجموعات الإدخال - تخصيص حسب موضع الأيقونات */
.form-elrakami-container.icons-right .form-elrakami-form .input-group input,
.form-elrakami-container.icons-right .form-elrakami-form .input-group select {
    border-radius: var(--form-input-radius) 0 0 var(--form-input-radius);
    text-align: right;
}

.form-elrakami-container.icons-left .form-elrakami-form .input-group input,
.form-elrakami-container.icons-left .form-elrakami-form .input-group select {
    border-radius: 0 var(--form-input-radius) var(--form-input-radius) 0;
    text-align: right;
}

.form-elrakami-form input::placeholder,
.form-elrakami-form textarea::placeholder {
    color: var(--form-placeholder-color);
    opacity: 1;
}

.form-elrakami-form input:focus,
.form-elrakami-form textarea:focus,
.form-elrakami-form select:focus {
    border-color: var(--form-input-focus-border);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.15);
}

/**
 * تعديلات خاصة لعناصر القائمة المنسدلة للتأكد من اتساقها مع التصميم
 */
.form-elrakami-form select {
    background-color: var(--form-input-bg);
    cursor: pointer;
    text-align: right;
    padding-right: 1rem;
    direction: rtl;
    /* إزالة السهم الافتراضي */
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/* إزالة السهم من جميع القوائم المنسدلة في النماذج */
.form-elrakami-container select,
.form-elrakami-shortcode-container select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: var(--form-input-bg) !important;
}

/* إزالة السهم من متصفح Internet Explorer */
.form-elrakami-container select::-ms-expand,
.form-elrakami-shortcode-container select::-ms-expand {
    display: none !important;
}

/* تخصيص حالات التحويم والتركيز */
.form-elrakami-form select:hover {
    border-color: var(--form-primary-color);
}

/* تأكيد على أن قوائم الولاية والبلدية تظهر بشكل صحيح */
.form-elrakami-form select[name="state"],
.form-elrakami-form select[name="municipality"] {
    background-color: var(--form-input-bg);
    width: 100%;
    height: auto;
    min-height: 2.5rem;
}

/**
 * زر الإرسال
 */

/* الأيقونة في الزر */
.form-elrakami-form .button-icon.left {
    margin-left: 10px;
    margin-right: 10px;
}

.form-elrakami-form .button-icon.right {
    margin-right: 10px;
    margin-left: 10px;
}

/**
 * زر الطلب عبر واتساب
 */
/* تم نقل تصميم زر الواتساب إلى التصميم الموحد أدناه */

/**
 * حاوية الأزرار الثانوية (السلة والواتساب)
 */
.form-elrakami-secondary-buttons-container {
    display: flex;
    gap: 10px;
    margin-top: 0.8rem;
    margin-bottom: 1rem;
}

/* عندما يكون هناك زران - يظهران بجانب بعضهما في جميع الشاشات */
.form-elrakami-secondary-buttons-container.two-buttons {
    justify-content: space-between;
}

.form-elrakami-secondary-buttons-container.two-buttons .form-elrakami-add-to-cart,
.form-elrakami-secondary-buttons-container.two-buttons .whatsapp-order-button {
    flex: 1;
    max-width: calc(50% - 5px);
    width: calc(50% - 5px);
    height: 40px;
    min-height: 40px;
    box-sizing: border-box;
}

/* عندما يكون هناك زر واحد فقط - يظهر في الوسط تماماً */
.form-elrakami-secondary-buttons-container.single-button {
    justify-content: center;
    align-items: center;
    text-align: center;
}

.form-elrakami-secondary-buttons-container.single-button .form-elrakami-add-to-cart,
.form-elrakami-secondary-buttons-container.single-button .whatsapp-order-button {
    max-width: 200px;
    min-width: 150px;
    width: auto;
    height: 40px;
    min-height: 40px;
    box-sizing: border-box;
    margin: 0 auto;
    flex: none;
}

/**
 * تصميم موحد للأزرار الثانوية (السلة والواتساب)
 */
.form-elrakami-form .form-elrakami-add-to-cart,
.form-elrakami-form .whatsapp-order-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    font-size: 0.85rem;
    font-weight: 600;
    border: none !important;
    cursor: pointer;
    transition: all 0.3s ease-in-out !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    text-align: center;
    text-decoration: none;
    min-width: 120px;
    height: 40px;
    min-height: 40px;
    line-height: 1.2;
    border-radius: 6px;
    box-sizing: border-box;
    margin: 0;
}

.form-elrakami-form .form-elrakami-add-to-cart:hover,
.form-elrakami-form .whatsapp-order-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.form-elrakami-form .form-elrakami-add-to-cart:active,
.form-elrakami-form .whatsapp-order-button:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* تنسيق أيقونات الأزرار */
.form-elrakami-form .form-elrakami-add-to-cart .button-icon,
.form-elrakami-form .whatsapp-order-button .button-icon {
    margin-left: 5px;
    font-size: 0.9rem;
}

/**
 * حاوية أزرار الطلب (للتوافق مع الإصدارات السابقة)
 */
.form-elrakami-buttons-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-top: 0.8rem;
}



/**
 * عنصر التنبيه (Toast)
 */
.form-elrakami-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 20px;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16);
    display: none;
    text-align: center;
    min-width: 250px;
    max-width: 80%;
}

.form-elrakami-toast.info {
    background-color: #3498db;
}

.form-elrakami-toast.success {
    background-color: #2ecc71;
}

.form-elrakami-toast.warning {
    background-color: #f39c12;
}

.form-elrakami-toast.error {
    background-color: #e74c3c;
}
.form-elrakami-form .form-elrakami-submit {
    display: block;
    width: 100%;
    padding: 1.2rem 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white !important;
    background-color: var(--button-color, var(--form-primary-color));
    border: none !important;
    border-radius: var(--form-input-radius);
    cursor: pointer;
    transition: all 0.3s ease-in-out !important;
    margin-top: 1.2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    text-align: center;
    min-height: 60px;
}

/* تطبيق التدرج اللوني للزر */
.form-elrakami-form .form-elrakami-submit.gradient {
    background-image: linear-gradient(var(--button-gradient-direction, to bottom), var(--button-color), var(--button-gradient-color)) !important;
    background-size: 100% !important;
    background-position: center !important;
}

/* أحجام مختلفة للزر */
.form-elrakami-form .form-elrakami-submit.button-size-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.form-elrakami-form .form-elrakami-submit.button-size-medium {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.form-elrakami-form .form-elrakami-submit.button-size-large {
    padding: 0.7rem 1.2rem;
    font-size: 0.95rem;
    font-weight: 600;
}

.form-elrakami-form .form-elrakami-submit:hover {
    opacity: 0.9;
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.form-elrakami-form .form-elrakami-submit:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
}

/* تأثيرات حركية للزر */
.form-elrakami-form .form-elrakami-submit.animation-pulse {
    animation: form_button_pulse 1.5s infinite;
}

.form-elrakami-form .form-elrakami-submit.animation-bounce {
    animation: form_button_bounce 1.5s infinite;
}

.form-elrakami-form .form-elrakami-submit.animation-tada {
    animation: form_button_tada 1.5s infinite;
}

.form-elrakami-form .form-elrakami-submit.animation-shake {
    animation: form_button_shake 1.5s infinite;
}

@keyframes form_button_pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes form_button_bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

@keyframes form_button_tada {
    0% { transform: scale(1); }
    10%, 20% { transform: scale(0.9) rotate(-3deg); }
    30%, 50%, 70%, 90% { transform: scale(1.1) rotate(3deg); }
    40%, 60%, 80% { transform: scale(1.1) rotate(-3deg); }
    100% { transform: scale(1) rotate(0); }
}

@keyframes form_button_shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/**
 * خيارات Checkbox و Radio
 */
.form-elrakami-form .checkbox-options,
.form-elrakami-form .radio-options {
    margin-top: 0.5rem;
}

.form-elrakami-form .checkbox-label,
.form-elrakami-form .radio-label {
    display: block;
    margin-bottom: 0.5rem;
    padding-right: 1.5rem;
    position: relative;
    cursor: pointer;
    font-weight: 400;
}

.form-elrakami-form .checkbox-label input,
.form-elrakami-form .radio-label input {
    position: absolute;
    right: 0;
    top: 0.25rem;
}

/**
 * رسائل الخطأ
 */
.form-elrakami-form .field-error {
    color: var(--form-error-color);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.form-elrakami-form input.error,
.form-elrakami-form textarea.error,
.form-elrakami-form select.error {
    border-color: var(--form-error-color);
}

/**
 * حاوية رسائل النموذج - تظهر دائماً تحت الأزرار الثانوية
 */
.form-elrakami-form .form-elrakami-message {
    margin-top: 1rem;
    padding: 0.75rem;
    border-radius: var(--form-input-radius);
    display: none;
    clear: both;
    order: 10; /* للتأكد من ظهورها بعد الأزرار */
}

.form-elrakami-form .form-elrakami-message.success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
    display: block;
}

.form-elrakami-form .form-elrakami-message.error {
    background-color: #fee2e2;
    color: #b91c1c;
    border: 1px solid #fecaca;
    display: block;
}

/* التأكد من أن الرسائل تظهر تحت الأزرار الثانوية */
.form-elrakami-secondary-buttons-container + .form-elrakami-message {
    margin-top: 1rem;
}

/**
 * المؤشر أثناء الإرسال
 */
.form-elrakami-loading {
    display: flex;
    justify-content: center;
    padding: 1rem 0;
    clear: both;
}

/**
 * المؤشر أثناء الإرسال
 */
.form-elrakami-loading {
    display: flex;
    justify-content: center;
    padding: 1rem 0;
}

.form-elrakami-loading .spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-top-color: var(--form-primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/**
 * قسم طرق الشحن
 */
.shipping-methods-container {
    margin-top: 1rem;
    margin-bottom: 1rem;
    border-radius: var(--form-input-radius);
    overflow: hidden;
}

/* رأس قسم طرق التوصيل */
.shipping-methods-header {
    margin-bottom: 1rem;
}

.shipping-methods-header h4 {
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0 0 0.4rem 0;
    color: var(--form-text-color);
    display: flex;
    align-items: center;
}

.shipping-methods-header h4 i {
    margin-left: 0.5rem;
    color: var(--form-primary-color);
}

/* حالة التحميل */
.loading-shipping-methods {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background-color: #f8fafc;
    border-radius: var(--form-input-radius);
    border: 1px dashed #cbd5e1;
}

.loading-shipping-methods p {
    margin-top: 0.75rem;
    color: var(--form-label-color);
}

/* خيارات طرق التوصيل - تصميم احترافي جديد */
.shipping-methods-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr); /* ثلاثة أعمدة افتراضيًا للكمبيوتر */
    gap: 12px; /* مسافة مناسبة بين بطاقات الشحن */
    margin: 1.2rem 0;
    width: 100%;
}

/* تخطيط عمود واحد - عند الحاجة لبطاقات أكبر */
.form-elrakami-form.single-column-shipping .shipping-methods-list {
    grid-template-columns: 1fr;
}

/* تخطيط عمودين - الأكثر استخدامًا */
.form-elrakami-form.two-column-shipping .shipping-methods-list {
    grid-template-columns: repeat(2, 1fr);
}

/* تخطيط أربعة أعمدة - للبطاقات الصغيرة جدًا */
.form-elrakami-form.four-column-shipping .shipping-methods-list {
    grid-template-columns: repeat(4, 1fr);
}

/* تجاوب تخطيط الأعمدة مع الشاشات الصغيرة */
@media (max-width: 768px) {
    .shipping-methods-list {
        grid-template-columns: repeat(2, 1fr); /* عمودين افتراضيًا للهواتف */
        gap: 10px; /* مسافة مناسبة بين طرق الشحن */
    }

    /* تطبيق عمودين على جميع الأشكال في وضع الهاتف للاتساق */
    .form-elrakami-form.single-column-shipping .shipping-methods-list,
    .form-elrakami-form.three-column-shipping .shipping-methods-list,
    .form-elrakami-form.four-column-shipping .shipping-methods-list {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* شاشات صغيرة جدًا مثل الهواتف الصغيرة */
@media (max-width: 380px) {
    .shipping-methods-list {
        gap: 8px; /* تقليل المسافة في الشاشات الصغيرة جدًا */
    }
}

/* تصميم احترافي وعملي لبطاقات طرق التوصيل */
.shipping-method-option {
    padding: 12px; /* هوامش داخلية أكبر للمظهر الأفضل */
    border-radius: 8px; /* استدارة زوايا مناسبة للتصميم الحديث */
    background-color: #ffffff; /* خلفية بيضاء ناصعة */
    border: 1px solid #e5e7eb; /* حدود خفيفة */
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03); /* ظل خفيف للعمق */
    overflow: hidden; /* لضمان عدم تجاوز المحتوى */
    min-height: 85px; /* ارتفاع مناسب لمحتوى طريقة الشحن */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* تأثير التحويم */
.shipping-method-option:hover {
    background-color: #f9fafb; /* خلفية خفيفة عند التحويم */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.06); /* ظل أكبر قليلاً */
    transform: translateY(-1px); /* ارتفاع طفيف */
    border-color: #d1d5db; /* لون حدود أغمق قليلاً عند التحويم */
}

/* البطاقة المحددة */
.shipping-method-option.selected {
    background-color: rgba(37, 99, 235, 0.04); /* خلفية خفيفة بلون أزرق */
    border-color: #3b82f6; /* حدود باللون الأساسي */
    box-shadow: 0 2px 5px rgba(59, 130, 246, 0.15); /* ظل بلون أزرق خفيف */
    position: relative;
}

/* إضافة مؤشر للعنصر المحدد */
.shipping-method-option.selected:before {
    content: '';
    position: absolute;
    top: 6px;
    right: 6px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #3b82f6;
}

/* إخفاء زر الراديو مع الاحتفاظ بإمكانية النقر */
.shipping-method-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0;
    cursor: pointer;
    z-index: 2;
}

/* تنظيم محتوى البطاقة */
.shipping-method-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 2px 0;
}

/* عنوان طريقة التوصيل بتنسيق أنيق */
.shipping-method-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #111827; /* لون داكن للعنوان */
    font-size: 0.9rem;
    text-align: center;
    line-height: 1.3;
}

/* وصف طريقة التوصيل خفيف وصغير */
.shipping-method-description {
    font-size: 0.75rem;
    color: #6b7280; /* لون رمادي متوسط */
    margin: 3px 0;
    text-align: center;
    line-height: 1.3;
}

/* سعر طريقة التوصيل بارز وبلون مميز */
.shipping-method-price {
    font-weight: 700;
    color: #3b82f6; /* لون أزرق للسعر */
    font-size: 1rem;
    margin-top: 7px;
    display: inline-block;
    text-align: center;
    background-color: rgba(59, 130, 246, 0.06); /* خلفية خفيفة جدًا */
    padding: 2px 8px;
    border-radius: 4px;
    line-height: 1.4;
}

/* رسائل الخطأ في قسم طرق التوصيل */
.shipping-methods-error,
.shipping-methods-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background-color: #f8fafc;
    border-radius: var(--form-input-radius);
    border: 1px dashed #cbd5e1;
    text-align: center;
}

.shipping-methods-error i,
.shipping-methods-empty i {
    font-size: 1.5rem;
    margin-bottom: 0.75rem;
}

.shipping-methods-error i {
    color: var(--form-error-color);
}

.shipping-methods-empty i {
    color: #94a3b8;
}

.shipping-methods-error p,
.shipping-methods-empty p {
    margin: 0;
    color: var(--form-label-color);
}

/**
 * العنوان
 */
.form-elrakami-address-fields {
    margin-top: 1.5rem;
}

/**
 * إجمالي السعر
 */
.total-price-container {
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: #f1f5f9;
    border-radius: var(--form-input-radius);
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
}

.total-price-container h4 {
    font-size: 1.1rem;
    margin: 0.5rem 0;
    color: var(--form-text-color);
    font-weight: 600;
}

.total-price-display {
    font-weight: 700;
    color: var(--form-primary-color);
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

/* تأثير تحديث السعر */
.total-price-display.price-updated {
    color: #10b981;
}

/* تفاصيل تقسيم السعر */
.price-breakdown {
    margin-bottom: 0.8rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.6rem;
}

.breakdown-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.4rem;
    font-size: 0.85rem;
}

.breakdown-label {
    color: var(--form-label-color);
}

.breakdown-value {
    font-weight: 500;
}

/**
 * شريط الطلب المثبت
 */
.form-elrakami-sticky-bar {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    background-color: white !important;
    box-shadow: 0 -3px 8px rgba(0, 0, 0, 0.08) !important;
    border-top: 1px solid var(--form-border-color) !important;
    padding: 10px 20px !important;
    z-index: 999999 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    transition: transform 0.3s ease !important;
    transform: translateY(0) !important; /* الشريط ظاهر دائمًا بشكل افتراضي */
    direction: rtl !important; /* تحديد اتجاه من اليمين لليسار */
}

.form-elrakami-sticky-bar.hidden {
    transform: translateY(100%) !important; /* إخفاء الشريط عند الحاجة */
}

/* تنسيق عنصر المنتج في الشريط السفلي */
.form-elrakami-sticky-bar-product {
    display: flex !important;
    align-items: center !important;
    font-size: 0.9rem !important;
    flex: 1 !important; /* يأخذ المساحة المتاحة */
    justify-content: flex-start !important; /* محاذاة العناصر إلى اليمين */
    margin-left: 15px !important; /* هامش بين معلومات المنتج والزر */
}

.form-elrakami-sticky-bar-product img {
    max-width: 50px !important;
    max-height: 50px !important;
    border-radius: 5px !important;
    margin-left: 10px !important; /* هامش بين الصورة والعنوان يمين الصورة */
    object-fit: contain !important;
}

.form-elrakami-sticky-bar-product-info {
    display: flex !important;
    flex-direction: column !important;
    text-align: right !important;
}

.form-elrakami-sticky-bar-mobile {
    display: none;
    width: 100%;
    padding: 15px;
}

.form-elrakami-sticky-bar-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.form-elrakami-sticky-bar-title {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--form-text-color);
}

.form-elrakami-sticky-bar-price {
    font-weight: 700;
    font-size: 1.2rem;
    color: var(--form-primary-color);
    line-height: 1;
}

.form-elrakami-sticky-bar-button {
    background-color: var(--sticky-button-color, var(--form-primary-color)) !important;
    color: var(--sticky-button-text-color, white) !important;
    padding: 1rem 1.5rem !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    border: none !important;
    cursor: pointer !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease !important;
    text-align: center !important;
    text-decoration: none !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    min-height: 55px !important;
}

.form-elrakami-sticky-bar-button.gradient {
    background-image: linear-gradient(var(--sticky-button-gradient-direction, to bottom), var(--sticky-button-color), var(--sticky-button-gradient-color)) !important;
    background-size: 100% !important;
    background-position: center !important;
}

.form-elrakami-sticky-bar-button:hover,
.form-elrakami-sticky-bar-button:focus {
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
    background-color: var(--form-primary-hover) !important;
    opacity: 0.95 !important;
}

.form-elrakami-sticky-bar-button:active {
    transform: translateY(0) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12) !important;
}

@media (max-width: 576px) {
    .form-elrakami-sticky-bar {
        padding: 10px 15px !important;
        flex-direction: row !important;
        justify-content: center !important; /* تمركز الزر في المنتصف */
    }

    /* إخفاء صورة واسم المنتج في وضع الهاتف */
    .form-elrakami-sticky-bar-product {
        display: none !important; /* إخفاء تام للصورة واسم المنتج */
    }

    .form-elrakami-sticky-bar-button {
        width: 100% !important; /* الزر يأخذ العرض كاملاً */
        max-width: 280px !important; /* تحديد الحد الأقصى للعرض */
        padding: 0.8rem 1.2rem !important; /* تقليل الحشو قليلاً للهاتف */
        min-height: 50px !important; /* ارتفاع أقل للهاتف */
    }

    .form-elrakami-sticky-bar-desktop {
        display: none !important;
    }

    .form-elrakami-sticky-bar-mobile {
        display: flex !important;
        flex-direction: column !important;
        width: 100% !important;
        align-items: center !important; /* توسيط الزر */
    }

    .form-elrakami-sticky-bar-info {
        display: none !important; /* إخفاء معلومات المنتج */
    }
}

/**
 * أنماط وظيفة طي ملخص الطلب
 */
.summary-header {
    cursor: pointer !important;
    user-select: none !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 0.75rem 1rem !important;
    background-color: #f8fafc !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: var(--form-input-radius) !important;
    margin-bottom: 0.5rem !important;
}

.summary-header:hover {
    background-color: #f1f5f9 !important;
    opacity: 0.9 !important;
}

.summary-header.collapsed {
    border-bottom-left-radius: var(--form-input-radius) !important;
    border-bottom-right-radius: var(--form-input-radius) !important;
}

.summary-header.expanded {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-bottom: none !important;
}

.summary-header .toggle-icon {
    margin-left: 10px !important;
    margin-right: 10px !important;
    transition: transform 0.3s ease !important;
    color: var(--form-primary-color) !important;
    font-size: 0.9rem !important;
}

.summary-header.expanded .toggle-icon {
    transform: rotate(180deg) !important;
}

/* تحسين مظهر محتوى الملخص */
.order-summary-content,
.summary-content,
.order-summary-details {
    border: 1px solid #e2e8f0 !important;
    border-top: none !important;
    border-radius: 0 0 var(--form-input-radius) var(--form-input-radius) !important;
    padding: 1rem !important;
    background-color: #ffffff !important;
    margin-bottom: 1rem !important;
}

/* تحسين للغات الأجنبية */
.form-elrakami-container.lang-fr .summary-header,
.form-elrakami-container.lang-en .summary-header,
.form-elrakami-container.lang-es .summary-header {
    text-align: left !important;
    direction: ltr !important;
}

.form-elrakami-container.lang-fr .summary-header .toggle-icon,
.form-elrakami-container.lang-en .summary-header .toggle-icon,
.form-elrakami-container.lang-es .summary-header .toggle-icon {
    float: left !important;
    margin-left: 0 !important;
    margin-right: 10px !important;
}

/**
 * إزالة شاملة وقوية لسهم القوائم المنسدلة من جميع المتصفحات
 */

/* إزالة السهم بقوة من جميع القوائم المنسدلة */
select,
select:focus,
select:hover,
select:active {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: transparent !important;
    border-radius: 0 !important;
}

/* إزالة السهم من متصفح Internet Explorer و Edge */
select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

select::-ms-value {
    background-color: transparent !important;
    color: inherit !important;
    border: none !important;
}

/* إزالة السهم من Firefox بطرق متعددة */
select {
    -moz-appearance: textfield !important;
    text-overflow: ellipsis !important;
}

@-moz-document url-prefix() {
    select {
        -moz-appearance: none !important;
        background-image: none !important;
    }
}

/* إزالة السهم من Safari و Chrome بقوة */
select {
    -webkit-appearance: none !important;
    -webkit-border-radius: 0 !important;
}

/* تطبيق قوي جداً على نماذج الإضافة */
.form-elrakami-container select,
.form-elrakami-shortcode-container select,
.form-elrakami-form select,
.form-elrakami-container select:focus,
.form-elrakami-shortcode-container select:focus,
.form-elrakami-form select:focus,
.form-elrakami-container select:hover,
.form-elrakami-shortcode-container select:hover,
.form-elrakami-form select:hover {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: var(--form-input-bg, #f8fafc) !important;
    border-radius: var(--form-input-radius, 6px) !important;
}

/* إزالة السهم من جميع حالات القوائم المنسدلة */
.form-elrakami-container select::-ms-expand,
.form-elrakami-shortcode-container select::-ms-expand,
.form-elrakami-form select::-ms-expand {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* إزالة السهم من جميع عناصر select بغض النظر عن الكلاس */
*[class*="form-elrakami"] select,
*[class*="form-elrakami"] select:focus,
*[class*="form-elrakami"] select:hover {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/* إزالة السهم من عناصر select داخل أي حاوي يحتوي على form-elrakami */
div[class*="form-elrakami"] select,
form[class*="form-elrakami"] select,
section[class*="form-elrakami"] select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
    background: var(--form-input-bg, #f8fafc) !important;
}

/* إزالة السهم من عناصر select بأسماء محددة */
select[name="state"],
select[name="municipality"],
select[name="country"] {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

