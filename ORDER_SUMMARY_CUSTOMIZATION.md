# إعدادات تخصيص ملخص الطلب - Form Elrakami

## نظرة عامة

تم إضافة إعدادات شاملة لتخصيص ملخص الطلب في Form Elrakami، مما يتيح للمستخدمين التحكم الكامل في مظهر وسلوك ملخص الطلب.

## الميزات الجديدة

### 1. إعدادات العرض

#### حالة عرض ملخص الطلب
- **ظاهر**: عرض ملخص الطلب بشكل طبيعي
- **مخفي بالكامل**: إخفاء ملخص الطلب وإظهار السعر الإجمالي في زر الطلب

#### الحالة الافتراضية للملخص (عند الظهور)
- **مفتوح افتراضياً**: ملخص الطلب يظهر مفتوحاً عند تحميل الصفحة
- **مغلق افتراضياً**: ملخص الطلب يظهر مغلقاً عند تحميل الصفحة

#### إظهار السعر في زر الطلب
- عند إخفاء ملخص الطلب، يمكن إظهار السعر الإجمالي بجانب نص زر الطلب
- يتحدث السعر تلقائياً عند تغيير الكمية أو طريقة الشحن

### 2. تخصيص المظهر

#### الألوان
- **لون خلفية ملخص الطلب**: تخصيص لون خلفية الحاوية الرئيسية
- **لون حدود ملخص الطلب**: تخصيص لون الحدود الخارجية
- **لون خلفية رأس الملخص**: تخصيص لون خلفية منطقة العنوان
- **لون عنوان الملخص**: تخصيص لون نص العنوان
- **لون الأسعار في الملخص**: تخصيص لون عرض الأسعار
- **لون خلفية الأسعار**: تخصيص لون خلفية عناصر الأسعار

#### التصميم
- **تدوير زوايا ملخص الطلب**: تحكم في مستوى تدوير الزوايا (0-30px)

### 3. معاينة مباشرة

- معاينة فورية لجميع التغييرات في صفحة الإعدادات
- تطبيق الألوان والإعدادات على المعاينة في الوقت الفعلي
- إمكانية اختبار وظيفة الطي والفتح في المعاينة

## الملفات المُحدثة

### ملفات الإدارة
- `admin/partials/form-settings-sidebar.php` - إضافة تبويب ملخص الطلب
- `admin/partials/tabs/tab-order-summary.php` - تبويب إعدادات ملخص الطلب الجديد
- `admin/partials/form-editor.php` - إضافة الإعدادات الافتراضية

### ملفات الواجهة الأمامية
- `public/partials/form-template.php` - تطبيق الإعدادات على ملخص الطلب
- `public/css/summary-toggle.css` - تحديث CSS لدعم المتغيرات المخصصة

### ملفات الاختبار
- `test-order-summary.html` - ملف اختبار لمعاينة الإعدادات

## كيفية الاستخدام

### 1. الوصول للإعدادات
1. انتقل إلى **Form Elrakami** > **النماذج**
2. اختر النموذج المراد تعديله أو أنشئ نموذجاً جديداً
3. انقر على تبويب **ملخص الطلب** في القائمة الجانبية

### 2. تخصيص العرض
1. اختر حالة عرض ملخص الطلب (ظاهر/مخفي)
2. إذا اخترت "ظاهر"، حدد الحالة الافتراضية (مفتوح/مغلق)
3. إذا اخترت "مخفي"، فعّل إظهار السعر في زر الطلب

### 3. تخصيص الألوان
1. استخدم منتقي الألوان لتخصيص كل عنصر
2. اضبط تدوير الزوايا باستخدام شريط التمرير
3. راقب التغييرات في المعاينة المباشرة

### 4. حفظ الإعدادات
1. انقر على **حفظ الإعدادات** في أسفل القائمة الجانبية
2. ستظهر رسالة تأكيد عند نجاح الحفظ

## الإعدادات الافتراضية

```php
'summary_display_mode' => 'visible',           // ظاهر
'summary_default_state' => 'open',             // مفتوح افتراضياً
'show_price_in_button' => 'yes',               // إظهار السعر في الزر
'summary_bg_color' => 'rgba(37, 99, 235, 0.05)',
'summary_border_color' => 'rgba(37, 99, 235, 0.1)',
'summary_header_bg_color' => '#f1f5f9',
'summary_title_color' => '#1e293b',
'summary_price_color' => '#3730a3',
'summary_price_bg_color' => 'rgba(55, 48, 163, 0.05)',
'summary_border_radius' => '12',
```

## المتغيرات CSS المستخدمة

```css
--summary-bg-color: لون خلفية الحاوية
--summary-border-color: لون الحدود
--summary-header-bg-color: لون خلفية الرأس
--summary-title-color: لون العنوان
--summary-price-color: لون الأسعار
--summary-price-bg-color: لون خلفية الأسعار
--summary-border-radius: تدوير الزوايا
```

## الوظائف JavaScript الجديدة

### في صفحة الإعدادات
- `updateSummaryPreview()` - تحديث المعاينة المباشرة
- معالجات الأحداث لتبديل حالة العرض والألوان

### في الواجهة الأمامية
- دعم الحالة الافتراضية للملخص (مفتوح/مغلق)
- تحديث السعر في زر الطلب عند إخفاء الملخص
- حفظ واستعادة حالة الطي/الفتح في التخزين المحلي

## ملاحظات تقنية

1. **التوافق مع الإصدارات السابقة**: جميع الإعدادات الجديدة لها قيم افتراضية تضمن عدم كسر النماذج الموجودة
2. **الأداء**: استخدام متغيرات CSS لتحسين الأداء وتقليل إعادة الرسم
3. **إمكانية الوصول**: الحفاظ على جميع ميزات إمكانية الوصول الموجودة
4. **الاستجابة**: جميع الإعدادات تعمل بشكل صحيح على جميع أحجام الشاشات

## الاختبار

تم إنشاء ملف `test-order-summary.html` لاختبار جميع الميزات الجديدة:
- عرض ملخص الطلب بالألوان الافتراضية
- عرض ملخص الطلب بألوان مخصصة (وردي وأخضر)
- عرض زر الطلب مع السعر عند إخفاء الملخص
- عناصر تحكم تفاعلية للاختبار

## إصلاح مشكلة حفظ الإعدادات

تم إصلاح مشكلة عدم حفظ إعدادات ملخص الطلب من خلال:

### 1. إضافة الإعدادات إلى دالة `ensure_form_settings`
```php
// إعدادات ملخص الطلب
$order_summary_settings = array(
    'summary_display_mode', 'summary_default_state', 'show_price_in_button',
    'summary_bg_color', 'summary_border_color', 'summary_header_bg_color',
    'summary_title_color', 'summary_price_color', 'summary_price_bg_color',
    'summary_border_radius'
);
```

### 2. إضافة الإعدادات إلى دالة `save_form`
```php
// إعدادات ملخص الطلب
'summary_display_mode' => isset($data['settings']['summary_display_mode']) ? sanitize_text_field($data['settings']['summary_display_mode']) : 'visible',
'summary_default_state' => isset($data['settings']['summary_default_state']) ? sanitize_text_field($data['settings']['summary_default_state']) : 'open',
// ... باقي الإعدادات
```

### 3. دمج الإعدادات الافتراضية مع الإعدادات الموجودة
```php
// دمج الإعدادات الافتراضية مع الإعدادات الموجودة لضمان وجود جميع الإعدادات الجديدة
$default_settings = array(
    'summary_display_mode' => 'visible',
    'summary_default_state' => 'open',
    // ... باقي الإعدادات الافتراضية
);

$form_settings = array_merge($default_settings, $form_settings ?: array());
```

### 4. إضافة تحميل منتقي الألوان
```php
// تحميل منتقي الألوان
wp_enqueue_script('wp-color-picker');
```

## استكشاف الأخطاء

### إذا لم يتم حفظ الإعدادات:
1. تحقق من سجل الأخطاء في WordPress
2. تأكد من أن جميع الإعدادات موجودة في `ensure_form_settings`
3. تأكد من أن جميع الإعدادات موجودة في `save_form`
4. تحقق من أن منتقي الألوان محمل بشكل صحيح

### ملفات التصحيح:
- `test-settings-save.php` - اختبار حفظ الإعدادات
- سجل الأخطاء في WordPress يحتوي على معلومات مفصلة عن حفظ الإعدادات

## المساهمة

عند إضافة ميزات جديدة لملخص الطلب:
1. أضف الإعدادات الجديدة في `tab-order-summary.php`
2. حدث الإعدادات الافتراضية في `form-editor.php`
3. **أضف الإعدادات إلى `ensure_form_settings` في `class-form-elrakami-admin.php`**
4. **أضف الإعدادات إلى `save_form` في `class-form-elrakami-admin.php`**
5. طبق الإعدادات في `form-template.php`
6. أضف CSS المطلوب في `summary-toggle.css`
7. اختبر جميع الحالات في `test-order-summary.html`
