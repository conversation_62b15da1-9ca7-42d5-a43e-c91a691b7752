<?php
/**
 * ملف اختبار لحفظ إعدادات ملخص الطلب
 * 
 * هذا الملف يساعد في اختبار ما إذا كانت إعدادات ملخص الطلب يتم حفظها بشكل صحيح
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

// دالة لاختبار حفظ الإعدادات
function test_order_summary_settings_save() {
    global $wpdb;
    
    echo "<h2>اختبار حفظ إعدادات ملخص الطلب</h2>";
    
    // الحصول على أول نموذج من قاعدة البيانات
    $table_name = $wpdb->prefix . 'form_elrakami_forms';
    $form = $wpdb->get_row("SELECT * FROM {$table_name} ORDER BY id DESC LIMIT 1");
    
    if (!$form) {
        echo "<p style='color: red;'>لا توجد نماذج في قاعدة البيانات</p>";
        return;
    }
    
    echo "<h3>معلومات النموذج:</h3>";
    echo "<p><strong>معرف النموذج:</strong> " . $form->id . "</p>";
    echo "<p><strong>عنوان النموذج:</strong> " . $form->title . "</p>";
    
    // فك تشفير الإعدادات
    $settings = Form_Elrakami_Helper::unserialize_data($form->settings);
    
    echo "<h3>إعدادات ملخص الطلب الحالية:</h3>";
    
    $order_summary_settings = array(
        'summary_display_mode' => 'حالة عرض ملخص الطلب',
        'summary_default_state' => 'الحالة الافتراضية للملخص',
        'show_price_in_button' => 'إظهار السعر في زر الطلب',
        'summary_bg_color' => 'لون خلفية ملخص الطلب',
        'summary_border_color' => 'لون حدود ملخص الطلب',
        'summary_header_bg_color' => 'لون خلفية رأس الملخص',
        'summary_title_color' => 'لون عنوان الملخص',
        'summary_price_color' => 'لون الأسعار في الملخص',
        'summary_price_bg_color' => 'لون خلفية الأسعار',
        'summary_border_radius' => 'تدوير زوايا ملخص الطلب'
    );
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الإعداد</th><th>الوصف</th><th>القيمة الحالية</th><th>الحالة</th></tr>";
    
    foreach ($order_summary_settings as $key => $description) {
        $value = isset($settings[$key]) ? $settings[$key] : 'غير محدد';
        $status = isset($settings[$key]) ? 
            "<span style='color: green;'>✓ موجود</span>" : 
            "<span style='color: red;'>✗ مفقود</span>";
        
        echo "<tr>";
        echo "<td><code>{$key}</code></td>";
        echo "<td>{$description}</td>";
        echo "<td>" . esc_html($value) . "</td>";
        echo "<td>{$status}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // اختبار محاكاة حفظ الإعدادات
    echo "<h3>اختبار محاكاة حفظ الإعدادات:</h3>";
    
    $test_data = array(
        'form_id' => $form->id,
        'form_title' => $form->title,
        'form_description' => $form->description,
        'form_status' => $form->status,
        'settings' => array(
            'summary_display_mode' => 'visible',
            'summary_default_state' => 'open',
            'show_price_in_button' => 'yes',
            'summary_bg_color' => 'rgba(37, 99, 235, 0.05)',
            'summary_border_color' => 'rgba(37, 99, 235, 0.1)',
            'summary_header_bg_color' => '#f1f5f9',
            'summary_title_color' => '#1e293b',
            'summary_price_color' => '#3730a3',
            'summary_price_bg_color' => 'rgba(55, 48, 163, 0.05)',
            'summary_border_radius' => '12'
        )
    );
    
    // محاكاة دالة ensure_form_settings
    $all_order_summary_settings = array(
        'summary_display_mode', 'summary_default_state', 'show_price_in_button',
        'summary_bg_color', 'summary_border_color', 'summary_header_bg_color',
        'summary_title_color', 'summary_price_color', 'summary_price_bg_color',
        'summary_border_radius'
    );
    
    echo "<h4>فحص وجود الإعدادات في البيانات المرسلة:</h4>";
    echo "<ul>";
    foreach ($all_order_summary_settings as $setting) {
        if (isset($test_data['settings'][$setting])) {
            echo "<li style='color: green;'>✓ {$setting}: " . $test_data['settings'][$setting] . "</li>";
        } else {
            echo "<li style='color: red;'>✗ {$setting}: مفقود</li>";
        }
    }
    echo "</ul>";
    
    // محاكاة معالجة الإعدادات في دالة save_form
    echo "<h4>محاكاة معالجة الإعدادات:</h4>";
    
    $processed_settings = array();
    $processed_settings['summary_display_mode'] = isset($test_data['settings']['summary_display_mode']) ? 
        sanitize_text_field($test_data['settings']['summary_display_mode']) : 'visible';
    $processed_settings['summary_default_state'] = isset($test_data['settings']['summary_default_state']) ? 
        sanitize_text_field($test_data['settings']['summary_default_state']) : 'open';
    $processed_settings['show_price_in_button'] = isset($test_data['settings']['show_price_in_button']) ? 
        sanitize_text_field($test_data['settings']['show_price_in_button']) : 'yes';
    $processed_settings['summary_bg_color'] = isset($test_data['settings']['summary_bg_color']) ? 
        sanitize_text_field($test_data['settings']['summary_bg_color']) : 'rgba(37, 99, 235, 0.05)';
    $processed_settings['summary_border_color'] = isset($test_data['settings']['summary_border_color']) ? 
        sanitize_text_field($test_data['settings']['summary_border_color']) : 'rgba(37, 99, 235, 0.1)';
    $processed_settings['summary_header_bg_color'] = isset($test_data['settings']['summary_header_bg_color']) ? 
        sanitize_hex_color($test_data['settings']['summary_header_bg_color']) : '#f1f5f9';
    $processed_settings['summary_title_color'] = isset($test_data['settings']['summary_title_color']) ? 
        sanitize_hex_color($test_data['settings']['summary_title_color']) : '#1e293b';
    $processed_settings['summary_price_color'] = isset($test_data['settings']['summary_price_color']) ? 
        sanitize_hex_color($test_data['settings']['summary_price_color']) : '#3730a3';
    $processed_settings['summary_price_bg_color'] = isset($test_data['settings']['summary_price_bg_color']) ? 
        sanitize_text_field($test_data['settings']['summary_price_bg_color']) : 'rgba(55, 48, 163, 0.05)';
    $processed_settings['summary_border_radius'] = isset($test_data['settings']['summary_border_radius']) ? 
        intval($test_data['settings']['summary_border_radius']) : 12;
    
    echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>الإعداد</th><th>القيمة المعالجة</th><th>النوع</th></tr>";
    
    foreach ($processed_settings as $key => $value) {
        $type = gettype($value);
        echo "<tr>";
        echo "<td><code>{$key}</code></td>";
        echo "<td>" . esc_html($value) . "</td>";
        echo "<td>{$type}</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>التوصيات:</h3>";
    echo "<ol>";
    echo "<li>تأكد من أن جميع إعدادات ملخص الطلب موجودة في دالة <code>ensure_form_settings</code></li>";
    echo "<li>تأكد من أن جميع إعدادات ملخص الطلب موجودة في دالة <code>save_form</code></li>";
    echo "<li>تأكد من أن الإعدادات الافتراضية يتم دمجها مع الإعدادات الموجودة</li>";
    echo "<li>تأكد من أن منتقي الألوان يعمل بشكل صحيح في صفحة الإعدادات</li>";
    echo "</ol>";
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (isset($_GET['test_order_summary'])) {
    test_order_summary_settings_save();
}
?>
